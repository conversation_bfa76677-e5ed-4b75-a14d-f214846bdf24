/**
 * 面部动画编辑器
 * 用于创建和编辑面部动画
 */
import * as THREE from 'three';
import { Entity } from '../core/Entity';
import { Component } from '../core/Component';
import { System } from '../core/System';
import { World } from '../core/World';
import { EventEmitter } from '../utils/EventEmitter';
import { FacialExpressionType, VisemeType } from './FacialAnimation';

/**
 * 面部动画关键帧
 */
export interface FacialAnimationKeyframe {
  /** 时间（秒） */
  time: number;
  /** 表情类型 */
  expression?: FacialExpressionType;
  /** 表情权重 */
  expressionWeight?: number;
  /** 口型类型 */
  viseme?: VisemeType;
  /** 口型权重 */
  visemeWeight?: number;
}

/**
 * 面部动画片段
 */
export interface FacialAnimationClip {
  /** 名称 */
  name: string;
  /** 持续时间（秒） */
  duration: number;
  /** 关键帧 */
  keyframes: FacialAnimationKeyframe[];
  /** 是否循环 */
  loop: boolean;
  /** 用户数据 */
  userData?: any;
}

/**
 * 面部动画编辑器配置
 */
export interface FacialAnimationEditorConfig {
  /** 是否启用调试 */
  debug?: boolean;
  /** 默认帧率 */
  defaultFrameRate?: number;
  /** 默认持续时间（秒） */
  defaultDuration?: number;
  /** 默认混合时间（秒） */
  defaultBlendTime?: number;
}

/**
 * 面部动画编辑器组件
 */
export class FacialAnimationEditorComponent extends Component {
  /** 组件类型 */
  static readonly type = 'FacialAnimationEditor';

  /** 动画片段 */
  private clips: Map<string, FacialAnimationClip> = new Map();
  /** 当前片段 */
  private currentClip: FacialAnimationClip | null = null;
  /** 当前时间 */
  private currentTime: number = 0;
  /** 是否播放中 */
  private isPlaying: boolean = false;
  /** 播放速度 */
  private playbackSpeed: number = 1.0;
  /** 帧率 */
  private frameRate: number = 30;
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /**
   * 构造函数
   * @param entity 实体
   */
  constructor(entity: Entity) {
    super(entity);
  }

  /**
   * 创建动画片段
   * @param name 名称
   * @param duration 持续时间（秒）
   * @param loop 是否循环
   * @returns 动画片段
   */
  public createClip(name: string, duration: number = 5.0, loop: boolean = false): FacialAnimationClip {
    const clip: FacialAnimationClip = {
      name,
      duration,
      keyframes: [],
      loop
    };

    this.clips.set(name, clip);
    return clip;
  }

  /**
   * 删除动画片段
   * @param name 名称
   * @returns 是否成功删除
   */
  public deleteClip(name: string): boolean {
    if (this.currentClip && this.currentClip.name === name) {
      this.currentClip = null;
    }
    return this.clips.delete(name);
  }

  /**
   * 获取动画片段
   * @param name 名称
   * @returns 动画片段，如果不存在则返回null
   */
  public getClip(name: string): FacialAnimationClip | null {
    return this.clips.get(name) || null;
  }

  /**
   * 获取所有动画片段
   * @returns 动画片段数组
   */
  public getClips(): FacialAnimationClip[] {
    return Array.from(this.clips.values());
  }

  /**
   * 设置当前片段
   * @param name 名称
   * @returns 是否成功设置
   */
  public setCurrentClip(name: string): boolean {
    const clip = this.clips.get(name);
    if (!clip) return false;

    this.currentClip = clip;
    this.currentTime = 0;
    this.eventEmitter.emit('clipChanged', { clip });
    return true;
  }

  /**
   * 获取当前片段
   * @returns 当前片段，如果不存在则返回null
   */
  public getCurrentClip(): FacialAnimationClip | null {
    return this.currentClip;
  }

  /**
   * 添加关键帧
   * @param time 时间（秒）
   * @param keyframe 关键帧数据
   * @returns 是否成功添加
   */
  public addKeyframe(time: number, keyframe: Partial<FacialAnimationKeyframe>): boolean {
    if (!this.currentClip) return false;

    // 创建关键帧
    const newKeyframe: FacialAnimationKeyframe = {
      time,
      ...keyframe
    };

    // 查找是否已存在相同时间的关键帧
    const existingIndex = this.currentClip.keyframes.findIndex(k => k.time === time);
    if (existingIndex >= 0) {
      // 更新现有关键帧
      this.currentClip.keyframes[existingIndex] = {
        ...this.currentClip.keyframes[existingIndex],
        ...newKeyframe
      };
    } else {
      // 添加新关键帧
      this.currentClip.keyframes.push(newKeyframe);
      // 按时间排序
      this.currentClip.keyframes.sort((a, b) => a.time - b.time);
    }

    this.eventEmitter.emit('keyframeAdded', { keyframe: newKeyframe });
    return true;
  }

  /**
   * 移除关键帧
   * @param time 时间（秒）
   * @returns 是否成功移除
   */
  public removeKeyframe(time: number): boolean {
    if (!this.currentClip) return false;

    const initialLength = this.currentClip.keyframes.length;
    this.currentClip.keyframes = this.currentClip.keyframes.filter(k => k.time !== time);
    
    const removed = this.currentClip.keyframes.length < initialLength;
    if (removed) {
      this.eventEmitter.emit('keyframeRemoved', { time });
    }
    
    return removed;
  }

  /**
   * 获取关键帧
   * @param time 时间（秒）
   * @returns 关键帧，如果不存在则返回null
   */
  public getKeyframe(time: number): FacialAnimationKeyframe | null {
    if (!this.currentClip) return null;
    return this.currentClip.keyframes.find(k => k.time === time) || null;
  }

  /**
   * 获取当前时间的插值关键帧
   * @returns 插值关键帧
   */
  public getInterpolatedKeyframe(): FacialAnimationKeyframe | null {
    if (!this.currentClip || this.currentClip.keyframes.length === 0) return null;

    // 如果只有一个关键帧，直接返回
    if (this.currentClip.keyframes.length === 1) {
      return { ...this.currentClip.keyframes[0] };
    }

    // 找到当前时间的前后关键帧
    let prevKeyframe: FacialAnimationKeyframe | null = null;
    let nextKeyframe: FacialAnimationKeyframe | null = null;

    for (let i = 0; i < this.currentClip.keyframes.length; i++) {
      if (this.currentClip.keyframes[i].time <= this.currentTime) {
        prevKeyframe = this.currentClip.keyframes[i];
      } else {
        nextKeyframe = this.currentClip.keyframes[i];
        break;
      }
    }

    // 如果没有前一个关键帧，使用最后一个
    if (!prevKeyframe && this.currentClip.loop) {
      prevKeyframe = this.currentClip.keyframes[this.currentClip.keyframes.length - 1];
    }

    // 如果没有后一个关键帧，使用第一个
    if (!nextKeyframe && this.currentClip.loop) {
      nextKeyframe = this.currentClip.keyframes[0];
    }

    // 如果仍然没有前后关键帧，返回null
    if (!prevKeyframe || !nextKeyframe) {
      return prevKeyframe || nextKeyframe;
    }

    // 计算插值因子
    let t = 0;
    if (nextKeyframe.time !== prevKeyframe.time) {
      t = (this.currentTime - prevKeyframe.time) / (nextKeyframe.time - prevKeyframe.time);
      t = Math.max(0, Math.min(1, t)); // 限制在[0,1]范围内
    }

    // 创建插值关键帧
    const interpolated: FacialAnimationKeyframe = {
      time: this.currentTime
    };

    // 插值表情
    if (prevKeyframe.expression && nextKeyframe.expression) {
      // 如果表情类型相同，插值权重
      if (prevKeyframe.expression === nextKeyframe.expression) {
        interpolated.expression = prevKeyframe.expression;
        interpolated.expressionWeight = this.lerp(
          prevKeyframe.expressionWeight || 0,
          nextKeyframe.expressionWeight || 0,
          t
        );
      } else {
        // 如果表情类型不同，根据t选择
        interpolated.expression = t < 0.5 ? prevKeyframe.expression : nextKeyframe.expression;
        interpolated.expressionWeight = t < 0.5 ? prevKeyframe.expressionWeight : nextKeyframe.expressionWeight;
      }
    } else if (prevKeyframe.expression) {
      interpolated.expression = prevKeyframe.expression;
      interpolated.expressionWeight = prevKeyframe.expressionWeight;
    } else if (nextKeyframe.expression) {
      interpolated.expression = nextKeyframe.expression;
      interpolated.expressionWeight = nextKeyframe.expressionWeight;
    }

    // 插值口型
    if (prevKeyframe.viseme && nextKeyframe.viseme) {
      // 如果口型类型相同，插值权重
      if (prevKeyframe.viseme === nextKeyframe.viseme) {
        interpolated.viseme = prevKeyframe.viseme;
        interpolated.visemeWeight = this.lerp(
          prevKeyframe.visemeWeight || 0,
          nextKeyframe.visemeWeight || 0,
          t
        );
      } else {
        // 如果口型类型不同，根据t选择
        interpolated.viseme = t < 0.5 ? prevKeyframe.viseme : nextKeyframe.viseme;
        interpolated.visemeWeight = t < 0.5 ? prevKeyframe.visemeWeight : nextKeyframe.visemeWeight;
      }
    } else if (prevKeyframe.viseme) {
      interpolated.viseme = prevKeyframe.viseme;
      interpolated.visemeWeight = prevKeyframe.visemeWeight;
    } else if (nextKeyframe.viseme) {
      interpolated.viseme = nextKeyframe.viseme;
      interpolated.visemeWeight = nextKeyframe.visemeWeight;
    }

    return interpolated;
  }

  /**
   * 线性插值
   * @param a 起始值
   * @param b 结束值
   * @param t 插值因子[0,1]
   * @returns 插值结果
   */
  private lerp(a: number, b: number, t: number): number {
    return a + (b - a) * t;
  }

  /**
   * 播放动画
   * @returns 是否成功开始播放
   */
  public play(): boolean {
    if (!this.currentClip) return false;
    this.isPlaying = true;
    this.eventEmitter.emit('playStarted', { clip: this.currentClip });
    return true;
  }

  /**
   * 暂停动画
   */
  public pause(): void {
    this.isPlaying = false;
    this.eventEmitter.emit('playPaused', { time: this.currentTime });
  }

  /**
   * 停止动画
   */
  public stop(): void {
    this.isPlaying = false;
    this.currentTime = 0;
    this.eventEmitter.emit('playStopped');
  }

  /**
   * 设置当前时间
   * @param time 时间（秒）
   */
  public setTime(time: number): void {
    if (!this.currentClip) return;
    this.currentTime = Math.max(0, Math.min(this.currentClip.duration, time));
    this.eventEmitter.emit('timeChanged', { time: this.currentTime });
  }

  /**
   * 获取当前时间
   * @returns 当前时间（秒）
   */
  public getTime(): number {
    return this.currentTime;
  }

  /**
   * 设置播放速度
   * @param speed 播放速度
   */
  public setPlaybackSpeed(speed: number): void {
    this.playbackSpeed = Math.max(0.1, speed);
  }

  /**
   * 获取播放速度
   * @returns 播放速度
   */
  public getPlaybackSpeed(): number {
    return this.playbackSpeed;
  }

  /**
   * 设置帧率
   * @param frameRate 帧率
   */
  public setFrameRate(frameRate: number): void {
    this.frameRate = Math.max(1, frameRate);
  }

  /**
   * 获取帧率
   * @returns 帧率
   */
  public getFrameRate(): number {
    return this.frameRate;
  }

  /**
   * 添加事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public addEventListener(event: string, callback: (data: any) => void): void {
    this.eventEmitter.on(event, callback);
  }

  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public removeEventListener(event: string, callback: (data: any) => void): void {
    this.eventEmitter.off(event, callback);
  }

  /**
   * 更新组件
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.isPlaying || !this.currentClip) return;

    // 更新时间
    this.currentTime += deltaTime * this.playbackSpeed;

    // 处理循环
    if (this.currentTime >= this.currentClip.duration) {
      if (this.currentClip.loop) {
        this.currentTime %= this.currentClip.duration;
        this.eventEmitter.emit('looped', { clip: this.currentClip });
      } else {
        this.currentTime = this.currentClip.duration;
        this.isPlaying = false;
        this.eventEmitter.emit('completed', { clip: this.currentClip });
      }
    }

    // 发送时间变化事件
    this.eventEmitter.emit('timeChanged', { time: this.currentTime });
  }
}
