/**
 * 动画系统模块
 * 导出所有动画系统相关的类和接口
 */

// 导出基础动画类
export * from './AnimationClip';
export * from './Animator';

// 导出混合空间
export * from './BlendSpace1D';
export * from './BlendSpace2D';

// 导出状态机
export * from './AnimationStateMachine';

// 导出重定向系统
export * from './AnimationRetargeting';
export * from './AnimationRetargeter';
export * from './RetargetingSystem';

// 导出骨骼动画
export * from './SkeletonAnimation';

// 导出动画事件系统
export * from './AnimationEvent';

// 导出动画实例化系统
export * from './AnimationInstancing';

// 导出GPU蒙皮系统
export * from './GPUSkinning';

// 导出面部动画系统
export * from './FacialAnimation';
export * from './FacialAnimationSystem';
export * from './LipSync';
export * from './FacialAnimationEditor';
export * from './FacialAnimationEditorSystem';
export * from './adapters';

// 导出AI动画合成系统
export * from './AIAnimationSynthesis';
export * from './AIAnimationSynthesisSystem';

// 导出物理驱动动画系统
export * from './PhysicsBasedAnimation';
export * from './physics';

// 导出Avatar动画系统
export * from '../avatar/components/AvatarAnimationComponent';
export * from '../avatar/components/AvatarRigComponent';
export * from '../avatar/systems/AvatarAnimationSystem';
