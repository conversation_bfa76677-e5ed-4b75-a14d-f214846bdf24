/**
 * 面部动画系统
 * 用于控制角色的面部表情和口型同步
 */
import * as THREE from 'three';
import { Entity } from '../core/Entity';
import { Component } from '../core/Component';
import { System } from '../core/System';
import { World } from '../core/World';
import { EventEmitter } from '../utils/EventEmitter';

/**
 * 面部表情类型
 */
export enum FacialExpressionType {
  NEUTRAL = 'neutral',
  HAPPY = 'happy',
  SAD = 'sad',
  ANGRY = 'angry',
  SURPRISED = 'surprised',
  FEARFUL = 'fearful',
  DISGUSTED = 'disgusted',
  CONTEMPT = 'contempt'
}

/**
 * 口型类型
 */
export enum VisemeType {
  SILENT = 'silent',   // 静默
  PP = 'pp',           // p, b, m
  FF = 'ff',           // f, v
  TH = 'th',           // th
  DD = 'dd',           // d, t, n
  KK = 'kk',           // k, g, ng
  CH = 'ch',           // ch, j, sh
  SS = 'ss',           // s, z
  NN = 'nn',           // n, l
  RR = 'rr',           // r
  AA = 'aa',           // a
  EE = 'ee',           // e
  IH = 'ih',           // i
  OH = 'oh',           // o
  OU = 'ou'            // u
}

/**
 * 面部动画配置
 */
export interface FacialAnimationConfig {
  /** 是否启用调试 */
  debug?: boolean;
  /** 是否自动检测音频 */
  autoDetectAudio?: boolean;
  /** 是否使用网络摄像头 */
  useWebcam?: boolean;
  /** 是否使用AI预测 */
  useAIPrediction?: boolean;
  /** 混合速度 */
  blendSpeed?: number;
  /** 平滑因子 */
  smoothingFactor?: number;
}

/**
 * 面部动画组件
 */
export class FacialAnimationComponent extends Component {
  /** 组件类型 */
  static readonly type = 'FacialAnimation';

  /** 当前表情 */
  private currentExpression: FacialExpressionType = FacialExpressionType.NEUTRAL;
  /** 目标表情 */
  private targetExpression: FacialExpressionType = FacialExpressionType.NEUTRAL;
  /** 表情权重 */
  private expressionWeight: number = 0;
  /** 表情混合速度 */
  private expressionBlendSpeed: number = 5.0;

  /** 当前口型 */
  private currentViseme: VisemeType = VisemeType.SILENT;
  /** 目标口型 */
  private targetViseme: VisemeType = VisemeType.SILENT;
  /** 口型权重 */
  private visemeWeight: number = 0;
  /** 口型混合速度 */
  private visemeBlendSpeed: number = 10.0;

  /** 表情混合映射 */
  private expressionBlendMap: Map<string, number> = new Map();
  /** 口型混合映射 */
  private visemeBlendMap: Map<string, number> = new Map();

  /** 是否启用 */
  private enabled: boolean = true;
  /** 是否使用平滑 */
  private useSmoothing: boolean = true;
  /** 平滑因子 */
  private smoothingFactor: number = 0.5;

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /**
   * 构造函数
   * @param entity 实体
   */
  constructor(entity: Entity) {
    super(entity);

    // 初始化表情混合映射
    for (const expression in FacialExpressionType) {
      this.expressionBlendMap.set(FacialExpressionType[expression], 0);
    }

    // 初始化口型混合映射
    for (const viseme in VisemeType) {
      this.visemeBlendMap.set(VisemeType[viseme], 0);
    }
  }

  /**
   * 设置表情
   * @param expression 表情类型
   * @param weight 权重
   * @param blendTime 混合时间（秒）
   */
  public setExpression(expression: FacialExpressionType, weight: number = 1.0, blendTime?: number): void {
    if (!this.enabled) return;

    this.targetExpression = expression;
    this.expressionWeight = weight;

    if (blendTime !== undefined) {
      this.expressionBlendSpeed = 1.0 / Math.max(0.001, blendTime);
    }

    this.eventEmitter.emit('expressionChange', { expression, weight });
  }

  /**
   * 获取当前表情
   * @returns 当前表情和权重
   */
  public getCurrentExpression(): { expression: FacialExpressionType, weight: number } {
    return {
      expression: this.currentExpression,
      weight: this.expressionWeight
    };
  }

  /**
   * 设置口型
   * @param viseme 口型类型
   * @param weight 权重
   * @param blendTime 混合时间（秒）
   */
  public setViseme(viseme: VisemeType, weight: number = 1.0, blendTime?: number): void {
    if (!this.enabled) return;

    this.targetViseme = viseme;
    this.visemeWeight = weight;

    if (blendTime !== undefined) {
      this.visemeBlendSpeed = 1.0 / Math.max(0.001, blendTime);
    }

    this.eventEmitter.emit('visemeChange', { viseme, weight });
  }

  /**
   * 获取当前口型
   * @returns 当前口型和权重
   */
  public getCurrentViseme(): { viseme: VisemeType, weight: number } {
    return {
      viseme: this.currentViseme,
      weight: this.visemeWeight
    };
  }

  /**
   * 获取表情混合映射
   * @returns 表情混合映射的副本
   */
  public getExpressionBlendMap(): Map<string, number> {
    return new Map(this.expressionBlendMap);
  }

  /**
   * 获取口型混合映射
   * @returns 口型混合映射的副本
   */
  public getVisemeBlendMap(): Map<string, number> {
    return new Map(this.visemeBlendMap);
  }

  /**
   * 重置表情
   */
  public resetExpression(): void {
    this.setExpression(FacialExpressionType.NEUTRAL, 0);
  }

  /**
   * 重置口型
   */
  public resetViseme(): void {
    this.setViseme(VisemeType.SILENT, 0);
  }

  /**
   * 启用组件
   */
  public enable(): void {
    this.enabled = true;
  }

  /**
   * 禁用组件
   */
  public disable(): void {
    this.enabled = false;
  }

  /**
   * 更新组件
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.enabled) return;

    // 更新表情混合
    this.updateExpressionBlend(deltaTime);

    // 更新口型混合
    this.updateVisemeBlend(deltaTime);
  }

  /**
   * 更新表情混合
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateExpressionBlend(deltaTime: number): void {
    // 计算混合因子
    const blendFactor = Math.min(1.0, this.expressionBlendSpeed * deltaTime);

    // 更新当前表情
    if (this.currentExpression !== this.targetExpression ||
        Math.abs(this.expressionBlendMap.get(this.targetExpression) || 0 - this.expressionWeight) > 0.01) {

      // 降低所有表情的权重
      for (const [expression, weight] of this.expressionBlendMap.entries()) {
        if (expression === this.targetExpression.toString()) {
          // 增加目标表情的权重
          const targetWeight = this.expressionWeight;
          const currentWeight = weight;
          const newWeight = this.useSmoothing
            ? currentWeight + (targetWeight - currentWeight) * blendFactor * (1.0 - this.smoothingFactor)
            : targetWeight;

          this.expressionBlendMap.set(expression, newWeight);
        } else {
          // 降低其他表情的权重
          const newWeight = this.useSmoothing
            ? weight * (1.0 - blendFactor)
            : 0;

          this.expressionBlendMap.set(expression, newWeight);
        }
      }

      this.currentExpression = this.targetExpression;
    }
  }

  /**
   * 更新口型混合
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateVisemeBlend(deltaTime: number): void {
    // 计算混合因子
    const blendFactor = Math.min(1.0, this.visemeBlendSpeed * deltaTime);

    // 更新当前口型
    if (this.currentViseme !== this.targetViseme ||
        Math.abs(this.visemeBlendMap.get(this.targetViseme) || 0 - this.visemeWeight) > 0.01) {

      // 降低所有口型的权重
      for (const [viseme, weight] of this.visemeBlendMap.entries()) {
        if (viseme === this.targetViseme.toString()) {
          // 增加目标口型的权重
          const targetWeight = this.visemeWeight;
          const currentWeight = weight;
          const newWeight = this.useSmoothing
            ? currentWeight + (targetWeight - currentWeight) * blendFactor * (1.0 - this.smoothingFactor)
            : targetWeight;

          this.visemeBlendMap.set(viseme, newWeight);
        } else {
          // 降低其他口型的权重
          const newWeight = this.useSmoothing
            ? weight * (1.0 - blendFactor)
            : 0;

          this.visemeBlendMap.set(viseme, newWeight);
        }
      }

      this.currentViseme = this.targetViseme;
    }
  }
}
