/**
 * AI动画合成系统
 * 用于管理和更新AI动画合成组件
 */
import * as THREE from 'three';
import { Entity } from '../core/Entity';
import { System } from '../core/System';
import { World } from '../core/World';
import { EventEmitter } from '../utils/EventEmitter';
import { AIAnimationSynthesisComponent, AIAnimationSynthesisConfig, AnimationGenerationRequest, AnimationGenerationResult } from './AIAnimationSynthesis';
import { AnimationClip } from './AnimationClip';
import { FacialAnimationClip } from './FacialAnimationEditor';
import { IAIAnimationModel, LocalAIAnimationModel } from './ai';

/**
 * AI动画合成系统
 */
export class AIAnimationSynthesisSystem extends System {
  /** 系统类型 */
  static readonly type = 'AIAnimationSynthesis';

  /** AI动画合成组件 */
  private components: Map<Entity, AIAnimationSynthesisComponent> = new Map();

  /** 配置 */
  private config: AIAnimationSynthesisConfig;

  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: AIAnimationSynthesisConfig = {
    debug: false,
    useLocalModel: true,
    batchSize: 1,
    sampleRate: 30,
    maxContextLength: 1024
  };

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** AI模型 */
  private aiModel: IAIAnimationModel | null = null;

  /** 模型是否已加载 */
  private modelLoaded: boolean = false;

  /** 模型加载进度 */
  private modelLoadProgress: number = 0;

  /**
   * 构造函数
   * @param world 世界
   * @param config 配置
   */
  constructor(world: World, config?: Partial<AIAnimationSynthesisConfig>) {
    super(world);
    this.config = { ...AIAnimationSynthesisSystem.DEFAULT_CONFIG, ...config };

    // 初始化模型
    this.initModel();
  }

  /**
   * 初始化AI模型
   */
  private async initModel(): Promise<void> {
    if (this.config.debug) {
      console.log('正在初始化AI模型...');
    }

    try {
      // 创建AI模型
      if (this.config.useLocalModel) {
        this.aiModel = new LocalAIAnimationModel({
          debug: this.config.debug,
          batchSize: this.config.batchSize
        });
      } else {
        // 使用远程模型或其他模型实现
        this.aiModel = new LocalAIAnimationModel({
          debug: this.config.debug,
          batchSize: this.config.batchSize
        });
      }

      // 初始化进度报告
      for (let i = 0; i <= 10; i++) {
        this.modelLoadProgress = i / 10;
        this.eventEmitter.emit('modelLoadProgress', { progress: this.modelLoadProgress });

        if (i < 10) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

      // 初始化模型
      const success = await this.aiModel.initialize();

      if (success) {
        this.modelLoaded = true;
        this.eventEmitter.emit('modelLoaded', { success: true });

        if (this.config.debug) {
          console.log('AI模型初始化完成');
        }
      } else {
        throw new Error('模型初始化失败');
      }
    } catch (error) {
      this.modelLoaded = false;
      this.aiModel = null;
      this.eventEmitter.emit('modelLoaded', { success: false, error });

      if (this.config.debug) {
        console.error('AI模型初始化失败:', error);
      }
    }
  }

  /**
   * 创建AI动画合成组件
   * @param entity 实体
   * @returns AI动画合成组件
   */
  public createAIAnimationSynthesis(entity: Entity): AIAnimationSynthesisComponent {
    // 检查是否已存在组件
    if (this.components.has(entity)) {
      return this.components.get(entity)!;
    }

    // 创建新组件
    const component = new AIAnimationSynthesisComponent(entity);
    this.components.set(entity, component);

    // 如果AI模型已加载，设置AI模型
    if (this.modelLoaded && this.aiModel) {
      component.setAIModel(this.aiModel);
    }

    if (this.config.debug) {
      console.log(`创建AI动画合成组件: ${entity.id}`);
    }

    return component;
  }

  /**
   * 移除AI动画合成组件
   * @param entity 实体
   */
  public removeAIAnimationSynthesis(entity: Entity): void {
    if (this.components.has(entity)) {
      this.components.delete(entity);

      if (this.config.debug) {
        console.log(`移除AI动画合成组件: ${entity.id}`);
      }
    }
  }

  /**
   * 获取AI动画合成组件
   * @param entity 实体
   * @returns AI动画合成组件，如果不存在则返回null
   */
  public getAIAnimationSynthesis(entity: Entity): AIAnimationSynthesisComponent | null {
    return this.components.get(entity) || null;
  }

  /**
   * 生成身体动画
   * @param entity 实体
   * @param prompt 提示文本
   * @param duration 持续时间（秒）
   * @param options 其他选项
   * @returns 请求ID
   */
  public generateBodyAnimation(
    entity: Entity,
    prompt: string,
    duration: number = 5.0,
    options: Partial<Omit<AnimationGenerationRequest, 'id' | 'prompt' | 'duration' | 'type'>> = {}
  ): string | null {
    const component = this.getAIAnimationSynthesis(entity);
    if (!component) return null;

    const request: Omit<AnimationGenerationRequest, 'id'> = {
      prompt,
      duration,
      type: 'body',
      loop: options.loop ?? false,
      referenceClip: options.referenceClip,
      style: options.style,
      intensity: options.intensity,
      seed: options.seed,
      userData: options.userData
    };

    return component.requestAnimation(request);
  }

  /**
   * 生成面部动画
   * @param entity 实体
   * @param prompt 提示文本
   * @param duration 持续时间（秒）
   * @param options 其他选项
   * @returns 请求ID
   */
  public generateFacialAnimation(
    entity: Entity,
    prompt: string,
    duration: number = 5.0,
    options: Partial<Omit<AnimationGenerationRequest, 'id' | 'prompt' | 'duration' | 'type'>> = {}
  ): string | null {
    const component = this.getAIAnimationSynthesis(entity);
    if (!component) return null;

    const request: Omit<AnimationGenerationRequest, 'id'> = {
      prompt,
      duration,
      type: 'facial',
      loop: options.loop ?? false,
      referenceClip: options.referenceClip,
      style: options.style,
      intensity: options.intensity,
      seed: options.seed,
      userData: options.userData
    };

    return component.requestAnimation(request);
  }

  /**
   * 生成组合动画
   * @param entity 实体
   * @param prompt 提示文本
   * @param duration 持续时间（秒）
   * @param options 其他选项
   * @returns 请求ID
   */
  public generateCombinedAnimation(
    entity: Entity,
    prompt: string,
    duration: number = 5.0,
    options: Partial<Omit<AnimationGenerationRequest, 'id' | 'prompt' | 'duration' | 'type'>> = {}
  ): string | null {
    const component = this.getAIAnimationSynthesis(entity);
    if (!component) return null;

    const request: Omit<AnimationGenerationRequest, 'id'> = {
      prompt,
      duration,
      type: 'combined',
      loop: options.loop ?? false,
      referenceClip: options.referenceClip,
      style: options.style,
      intensity: options.intensity,
      seed: options.seed,
      userData: options.userData
    };

    return component.requestAnimation(request);
  }

  /**
   * 取消生成请求
   * @param entity 实体
   * @param requestId 请求ID
   * @returns 是否成功取消
   */
  public cancelRequest(entity: Entity, requestId: string): boolean {
    const component = this.getAIAnimationSynthesis(entity);
    if (!component) return false;

    return component.cancelRequest(requestId);
  }

  /**
   * 获取生成结果
   * @param entity 实体
   * @param requestId 请求ID
   * @returns 生成结果，如果不存在则返回null
   */
  public getResult(entity: Entity, requestId: string): AnimationGenerationResult | null {
    const component = this.getAIAnimationSynthesis(entity);
    if (!component) return null;

    return component.getResult(requestId);
  }

  /**
   * 添加事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public addEventListener(event: string, callback: (data: any) => void): void {
    this.eventEmitter.on(event, callback);
  }

  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public removeEventListener(event: string, callback: (data: any) => void): void {
    this.eventEmitter.off(event, callback);
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 更新所有AI动画合成组件
    for (const component of this.components.values()) {
      component.update(deltaTime);
    }
  }
}
