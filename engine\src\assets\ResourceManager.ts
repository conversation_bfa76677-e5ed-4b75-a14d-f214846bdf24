/**
 * 资源管理器类
 * 负责资源的加载、缓存和释放
 */
import { EventEmitter } from '../utils/EventEmitter';

/**
 * 资产类型
 */
export enum AssetType {
  TEXTURE = 'texture',
  MODEL = 'model',
  MATERIAL = 'material',
  AUDIO = 'audio',
  FONT = 'font',
  SHADER = 'shader',
  JSON = 'json',
  TEXT = 'text',
  BINARY = 'binary',
  CUBEMAP = 'cubemap',
  UNKNOWN = 'unknown'
}

/**
 * 资源状态
 */
export enum ResourceState {
  /** 未加载 */
  UNLOADED = 'unloaded',
  /** 加载中 */
  LOADING = 'loading',
  /** 已加载 */
  LOADED = 'loaded',
  /** 加载失败 */
  ERROR = 'error',
}

/**
 * 资源信息
 */
export interface ResourceInfo {
  /** 资源ID */
  id: string;
  /** 资源类型 */
  type: AssetType;
  /** 资源URL */
  url: string;
  /** 资源数据 */
  data: any;
  /** 资源状态 */
  state: ResourceState;
  /** 引用计数 */
  refCount: number;
  /** 上次访问时间 */
  lastAccessTime: number;
  /** 资源大小（字节） */
  size: number;
  /** 加载错误 */
  error?: Error;
}

/**
 * 资源管理器选项
 */
export interface ResourceManagerOptions {
  /** 最大缓存大小（字节） */
  maxCacheSize?: number;
  /** 缓存清理阈值（0-1） */
  cleanupThreshold?: number;
  /** 缓存清理间隔（毫秒） */
  cleanupInterval?: number;
  /** 是否启用自动清理 */
  autoCleanup?: boolean;
  /** 是否启用预加载 */
  enablePreload?: boolean;
  /** 最大并发加载数 */
  maxConcurrentLoads?: number;
}

/**
 * 资源管理器
 */
export class ResourceManager extends EventEmitter {
  /** 资源映射 */
  private resources: Map<string, ResourceInfo> = new Map();

  /** 加载中的资源 */
  private loadingResources: Map<string, Promise<any>> = new Map();

  /** 最大缓存大小（字节） */
  private maxCacheSize: number;

  /** 当前缓存大小（字节） */
  private currentCacheSize: number = 0;

  /** 缓存清理阈值（0-1） */
  private cleanupThreshold: number;

  /** 缓存清理间隔（毫秒） */
  private cleanupInterval: number;

  /** 是否启用自动清理 */
  private autoCleanup: boolean;

  /** 是否启用预加载 */
  private enablePreload: boolean;

  /** 最大并发加载数 */
  private maxConcurrentLoads: number;

  /** 当前并发加载数 */
  private currentConcurrentLoads: number = 0;

  /** 加载队列 */
  private loadQueue: Array<{
    id: string;
    type: AssetType;
    url: string;
    resolve: (data: any) => void;
    reject: (error: Error) => void;
  }> = [];

  /** 清理定时器ID */
  private cleanupTimerId: number | null = null;

  /** 是否已初始化 */
  private initialized: boolean = false;

  /**
   * 创建资源管理器实例
   * @param options 资源管理器选项
   */
  constructor(options: ResourceManagerOptions = {}) {
    super();

    // 设置选项
    this.maxCacheSize = options.maxCacheSize || 1024 * 1024 * 100; // 默认100MB
    this.cleanupThreshold = options.cleanupThreshold || 0.8; // 默认80%
    this.cleanupInterval = options.cleanupInterval || 60000; // 默认1分钟
    this.autoCleanup = options.autoCleanup !== undefined ? options.autoCleanup : true;
    this.enablePreload = options.enablePreload !== undefined ? options.enablePreload : true;
    this.maxConcurrentLoads = options.maxConcurrentLoads || 6;
  }

  /**
   * 初始化资源管理器
   */
  public initialize(): void {
    if (this.initialized) {
      return;
    }

    // 如果启用自动清理，则启动清理定时器
    if (this.autoCleanup) {
      this.startCleanupTimer();
    }

    this.initialized = true;
    this.emit('initialized');
  }

  /**
   * 加载资源
   * @param id 资源ID
   * @param type 资源类型
   * @param url 资源URL
   * @returns Promise，解析为资源数据
   */
  public async load(id: string, type: AssetType, url: string): Promise<any> {
    // 检查资源是否已存在
    if (this.resources.has(id)) {
      const resource = this.resources.get(id)!;

      // 如果已加载，则更新访问时间并返回数据
      if (resource.state === ResourceState.LOADED) {
        resource.lastAccessTime = Date.now();
        resource.refCount++;
        return resource.data;
      }

      // 如果加载失败，则重新加载
      if (resource.state === ResourceState.ERROR) {
        resource.state = ResourceState.UNLOADED;
      }

      // 如果未加载，则继续加载流程
    }

    // 检查是否已在加载中
    if (this.loadingResources.has(id)) {
      return this.loadingResources.get(id);
    }

    // 创建加载Promise
    const loadPromise = new Promise<any>((resolve, reject) => {
      // 如果当前并发加载数达到最大值，则加入队列
      if (this.currentConcurrentLoads >= this.maxConcurrentLoads) {
        this.loadQueue.push({ id, type, url, resolve, reject });
        return;
      }

      // 增加并发加载计数
      this.currentConcurrentLoads++;

      // 更新资源状态
      this.updateResourceState(id, type, url, ResourceState.LOADING);

      // 发出加载开始事件
      this.emit('loadStart', { id, type, url });

      // 执行实际加载
      this.loadResource(id, type, url)
        .then(data => {
          // 更新资源状态
          this.updateResourceState(id, type, url, ResourceState.LOADED, data);

          // 发出加载完成事件
          this.emit('loadComplete', { id, type, url, data });

          // 减少并发加载计数
          this.currentConcurrentLoads--;

          // 从加载中资源映射中移除
          this.loadingResources.delete(id);

          // 处理队列中的下一个加载请求
          this.processNextQueuedLoad();

          resolve(data);
        })
        .catch(error => {
          // 更新资源状态
          this.updateResourceState(id, type, url, ResourceState.ERROR, undefined, error);

          // 发出加载错误事件
          this.emit('loadError', { id, type, url, error });

          // 减少并发加载计数
          this.currentConcurrentLoads--;

          // 从加载中资源映射中移除
          this.loadingResources.delete(id);

          // 处理队列中的下一个加载请求
          this.processNextQueuedLoad();

          reject(error);
        });
    });

    // 添加到加载中资源映射
    this.loadingResources.set(id, loadPromise);

    return loadPromise;
  }

  /**
   * 处理队列中的下一个加载请求
   */
  private processNextQueuedLoad(): void {
    // 如果队列为空，则返回
    if (this.loadQueue.length === 0) {
      return;
    }

    // 从队列中取出下一个加载请求
    const { id, type, url, resolve, reject } = this.loadQueue.shift()!;

    // 加载资源
    this.load(id, type, url)
      .then(resolve)
      .catch(reject);
  }

  /**
   * 实际加载资源
   * @param id 资源ID
   * @param type 资源类型
   * @param url 资源URL
   * @returns Promise，解析为资源数据
   */
  private async loadResource(_id: string, type: AssetType, url: string): Promise<any> {
    try {
      // 这里应该调用实际的加载逻辑，例如AssetLoader
      // 为了简化，这里使用fetch API加载资源
      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      let data: any;

      // 根据资源类型处理响应
      switch (type) {
        case AssetType.JSON:
          data = await response.json();
          break;

        case AssetType.TEXT:
          data = await response.text();
          break;

        case AssetType.BINARY:
          data = await response.arrayBuffer();
          break;

        default:
          // 对于其他类型，使用blob
          data = await response.blob();
          break;
      }

      return data;
    } catch (error) {
      throw new Error(`加载资源失败: ${(error as Error).message}`);
    }
  }

  /**
   * 更新资源状态
   * @param id 资源ID
   * @param type 资源类型
   * @param url 资源URL
   * @param state 资源状态
   * @param data 资源数据
   * @param error 加载错误
   */
  private updateResourceState(
    id: string,
    type: AssetType,
    url: string,
    state: ResourceState,
    data?: any,
    error?: Error
  ): void {
    // 获取现有资源或创建新资源
    let resource = this.resources.get(id);

    if (!resource) {
      resource = {
        id,
        type,
        url,
        data: undefined,
        state: ResourceState.UNLOADED,
        refCount: 0,
        lastAccessTime: Date.now(),
        size: 0,
      };

      this.resources.set(id, resource);
    }

    // 更新资源状态
    resource.state = state;

    // 如果提供了数据，则更新数据和大小
    if (data !== undefined) {
      // 如果之前有数据，则减少缓存大小
      if (resource.data) {
        this.currentCacheSize -= resource.size;
      }

      resource.data = data;
      resource.refCount = 1;
      resource.lastAccessTime = Date.now();

      // 计算资源大小
      resource.size = this.calculateResourceSize(data);

      // 增加缓存大小
      this.currentCacheSize += resource.size;

      // 如果超过最大缓存大小，则触发清理
      if (this.currentCacheSize > this.maxCacheSize * this.cleanupThreshold) {
        this.cleanup();
      }
    }

    // 如果提供了错误，则更新错误
    if (error !== undefined) {
      resource.error = error;
    }
  }

  /**
   * 计算资源大小
   * @param data 资源数据
   * @returns 资源大小（字节）
   */
  private calculateResourceSize(data: any): number {
    if (!data) {
      return 0;
    }

    // 根据数据类型计算大小
    if (data instanceof ArrayBuffer) {
      return data.byteLength;
    } else if (data instanceof Blob) {
      return data.size;
    } else if (typeof data === 'string') {
      return data.length * 2; // 假设UTF-16编码
    } else if (data instanceof Object) {
      // 对于对象，使用JSON字符串长度作为估计
      try {
        return JSON.stringify(data).length * 2;
      } catch (error) {
        return 1024; // 默认1KB
      }
    }

    return 1024; // 默认1KB
  }

  /**
   * 释放资源
   * @param id 资源ID
   * @returns 是否成功释放
   */
  public release(id: string): boolean {
    // 检查资源是否存在
    if (!this.resources.has(id)) {
      return false;
    }

    const resource = this.resources.get(id)!;

    // 减少引用计数
    resource.refCount--;

    // 如果引用计数为0，则考虑释放资源
    if (resource.refCount <= 0) {
      // 如果资源已加载，则减少缓存大小
      if (resource.state === ResourceState.LOADED && resource.data) {
        this.currentCacheSize -= resource.size;
      }

      // 清除资源数据
      resource.data = undefined;
      resource.state = ResourceState.UNLOADED;
      resource.refCount = 0;

      // 发出资源释放事件
      this.emit('resourceReleased', { id });

      return true;
    }

    return false;
  }

  /**
   * 获取资源
   * @param id 资源ID
   * @returns 资源信息，如果不存在则返回null
   */
  public getResource(id: string): ResourceInfo | null {
    const resource = this.resources.get(id);

    if (!resource) {
      return null;
    }

    // 更新访问时间
    resource.lastAccessTime = Date.now();

    return resource;
  }

  /**
   * 获取资源数据
   * @param id 资源ID
   * @returns 资源数据，如果不存在或未加载则返回null
   */
  public getResourceData(id: string): any {
    const resource = this.getResource(id);

    if (!resource || resource.state !== ResourceState.LOADED) {
      return null;
    }

    return resource.data;
  }

  /**
   * 预加载资源
   * @param id 资源ID
   * @param type 资源类型
   * @param url 资源URL
   * @returns Promise，解析为资源数据
   */
  public preload(id: string, type: AssetType, url: string): Promise<any> {
    // 如果未启用预加载，则直接返回
    if (!this.enablePreload) {
      return Promise.resolve(null);
    }

    // 调用加载方法
    return this.load(id, type, url);
  }

  /**
   * 批量预加载资源
   * @param resources 资源数组
   * @param onProgress 进度回调
   * @returns Promise，解析为资源数据映射
   */
  public async preloadBatch(
    resources: Array<{ id: string; type: AssetType; url: string }>,
    onProgress?: (loaded: number, total: number) => void
  ): Promise<Map<string, any>> {
    // 如果未启用预加载，则直接返回
    if (!this.enablePreload) {
      return Promise.resolve(new Map());
    }

    const total = resources.length;
    let loaded = 0;
    const results = new Map<string, any>();

    // 加载每个资源
    for (const { id, type, url } of resources) {
      try {
        const data = await this.load(id, type, url);
        results.set(id, data);
      } catch (error) {
        console.error(`预加载资源 ${id} 失败:`, error);
      }

      loaded++;

      if (onProgress) {
        onProgress(loaded, total);
      }
    }

    return results;
  }

  /**
   * 清理缓存
   */
  public cleanup(): void {
    // 如果缓存大小未超过阈值，则不需要清理
    if (this.currentCacheSize <= this.maxCacheSize * this.cleanupThreshold) {
      return;
    }

    // 获取所有资源
    const resources = Array.from(this.resources.values());

    // 按照最后访问时间排序（最早访问的在前面）
    resources.sort((a, b) => a.lastAccessTime - b.lastAccessTime);

    // 计算需要释放的空间
    const targetSize = this.maxCacheSize * 0.7; // 释放到70%
    const sizeToFree = this.currentCacheSize - targetSize;
    let freedSize = 0;

    // 释放资源直到达到目标大小
    for (const resource of resources) {
      // 如果资源未加载或引用计数大于0，则跳过
      if (resource.state !== ResourceState.LOADED || resource.refCount > 0) {
        continue;
      }

      // 释放资源
      this.release(resource.id);

      // 增加已释放大小
      freedSize += resource.size;

      // 如果已释放足够空间，则停止
      if (freedSize >= sizeToFree) {
        break;
      }
    }

    // 发出缓存清理事件
    this.emit('cacheCleanup', { freedSize });
  }

  /**
   * 启动清理定时器
   */
  private startCleanupTimer(): void {
    // 如果已有定时器，则先停止
    if (this.cleanupTimerId !== null) {
      this.stopCleanupTimer();
    }

    // 启动新定时器
    this.cleanupTimerId = window.setInterval(() => {
      this.cleanup();
    }, this.cleanupInterval);
  }

  /**
   * 停止清理定时器
   */
  private stopCleanupTimer(): void {
    if (this.cleanupTimerId !== null) {
      window.clearInterval(this.cleanupTimerId);
      this.cleanupTimerId = null;
    }
  }

  /**
   * 获取缓存统计信息
   * @returns 缓存统计信息
   */
  public getCacheStats(): {
    totalResources: number;
    loadedResources: number;
    currentCacheSize: number;
    maxCacheSize: number;
    usagePercentage: number;
  } {
    // 计算已加载资源数量
    const loadedResources = Array.from(this.resources.values()).filter(
      resource => resource.state === ResourceState.LOADED
    ).length;

    return {
      totalResources: this.resources.size,
      loadedResources,
      currentCacheSize: this.currentCacheSize,
      maxCacheSize: this.maxCacheSize,
      usagePercentage: (this.currentCacheSize / this.maxCacheSize) * 100,
    };
  }

  /**
   * 清空缓存
   */
  public clearCache(): void {
    // 释放所有资源
    for (const id of this.resources.keys()) {
      this.release(id);
    }

    // 清空资源映射
    this.resources.clear();

    // 重置缓存大小
    this.currentCacheSize = 0;

    // 发出缓存清空事件
    this.emit('cacheCleared');
  }

  /**
   * 销毁资源管理器
   */
  public dispose(): void {
    // 停止清理定时器
    this.stopCleanupTimer();

    // 清空缓存
    this.clearCache();

    // 清空加载队列
    this.loadQueue = [];

    // 清空加载中资源映射
    this.loadingResources.clear();

    // 移除所有事件监听器
    this.removeAllListeners();

    this.initialized = false;
  }
}
